/**
 * ai_tutor_page.dart - AI-Powered Tutoring Interface for Students
 *
 * English: This page provides an interactive AI tutoring experience for students in the
 * Sasthra application. Students can ask questions, get explanations, solve problems,
 * and receive personalized learning assistance through an AI-powered chat interface.
 * The AI tutor helps with JEE/NEET preparation, concept clarification, and study guidance.
 *
 * Tanglish: Inga students ku AI-powered tutoring experience provide pannum page irukku.
 * Questions kekka mudiyum, explanations vaanga mudiyum, problems solve panna mudiyum.
 * AI tutor JEE/NEET preparation ku help pannum, concepts clear pannum, study guidance
 * kudukum. Interactive chat interface la personalized learning assistance irukku.
 *
 * Key Features:
 * - Interactive chat interface with AI tutor
 * - Real-time question answering and explanations
 * - Subject-specific tutoring (Physics, Chemistry, Math, Biology)
 * - Step-by-step problem solving guidance
 * - Personalized learning recommendations
 * - Chat history and conversation persistence
 * - Multi-media support (text, images, equations)
 * - Learning progress tracking
 *
 * AI Capabilities:
 * - Concept explanation and clarification
 * - Problem-solving with step-by-step solutions
 * - Practice question generation
 * - Doubt resolution and Q&A
 * - Study plan recommendations
 * - Performance analysis and feedback
 *
 * UI Components:
 * - Chat interface with message bubbles
 * - Question input field with send button
 * - Loading indicators for AI responses
 * - Chat history with scroll functionality
 * - Subject selection and filtering options
 */

import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/widgets/base_page.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/logger.dart';

/**
 * StudentAITutorPage - AI Tutoring Interface Widget for Students
 *
 * English: StatefulWidget that provides AI-powered tutoring through interactive chat.
 * Tanglish: Students ku AI tutoring provide panna vendiya interactive chat widget.
 */
class StudentAITutorPage extends StatefulWidget {
  const StudentAITutorPage({Key? key}) : super(key: key);

  @override
  State<StudentAITutorPage> createState() => _StudentAITutorPageState();
}

class _StudentAITutorPageState extends State<StudentAITutorPage> {
  final TextEditingController _questionController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _isLoading = false;
  List<Map<String, dynamic>> _chatHistory = [];

  @override
  void initState() {
    super.initState();
    _initializeChat();
  }

  void _initializeChat() {
    setState(() {
      _chatHistory = [
        {
          'type': 'ai',
          'message': 'Hello! I\'m your AI Tutor. I can help you with Physics, Chemistry, Mathematics, and Biology questions. What would you like to learn today?',
          'timestamp': DateTime.now(),
        }
      ];
    });
  }

  @override
  Widget build(BuildContext context) {
    return BasePage(
      title: 'AI Tutor',
      subtitle: 'Get instant help with your studies',
      breadcrumbs: const ['Dashboard', 'Student', 'AI Tutor'],
      child: _buildAITutorContent(),
    );
  }

  Widget _buildAITutorContent() {
    return Column(
      children: [
        _buildQuickTopics(),
        Expanded(child: _buildChatArea()),
        _buildInputArea(),
      ],
    );
  }

  Widget _buildQuickTopics() {
    final topics = [
      {'title': 'Physics', 'icon': Icons.science, 'color': Colors.blue},
      {'title': 'Chemistry', 'icon': Icons.biotech, 'color': Colors.green},
      {'title': 'Mathematics', 'icon': Icons.calculate, 'color': Colors.orange},
      {'title': 'Biology', 'icon': Icons.local_florist, 'color': Colors.purple},
    ];

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Topics',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            height: 80,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: topics.length,
              itemBuilder: (context, index) {
                final topic = topics[index];
                return GestureDetector(
                  onTap: () => _selectTopic(topic['title'] as String),
                  child: Container(
                    width: 80,
                    margin: EdgeInsets.only(right: index < topics.length - 1 ? 12 : 0),
                    decoration: BoxDecoration(
                      color: AppTheme.surfaceColor,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppTheme.borderColor),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: (topic['color'] as Color).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Icon(
                            topic['icon'] as IconData,
                            color: topic['color'] as Color,
                            size: 16,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          topic['title'] as String,
                          style: AppTheme.bodySmall.copyWith(
                            color: AppTheme.textPrimary,
                            fontSize: 10,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ).animate(delay: Duration(milliseconds: 100 * index))
                    .fadeIn()
                    .scale(begin: const Offset(0.8, 0.8));
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChatArea() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        controller: _scrollController,
        itemCount: _chatHistory.length,
        itemBuilder: (context, index) {
          final message = _chatHistory[index];
          return _buildChatMessage(message, index);
        },
      ),
    );
  }

  Widget _buildChatMessage(Map<String, dynamic> message, int index) {
    final isAI = message['type'] == 'ai';
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (isAI) ...[
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                Icons.psychology,
                color: AppTheme.primaryColor,
                size: 16,
              ),
            ),
            const SizedBox(width: 12),
          ],
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isAI ? AppTheme.surfaceColor : AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isAI ? AppTheme.borderColor : AppTheme.primaryColor.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message['message'],
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatTime(message['timestamp']),
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.textTertiary,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (!isAI) ...[
            const SizedBox(width: 12),
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: AppTheme.primaryColor,
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Icon(
                Icons.person,
                color: Colors.white,
                size: 16,
              ),
            ),
          ],
        ],
      ),
    ).animate(delay: Duration(milliseconds: 100 * index))
        .fadeIn()
        .slideY(begin: 0.2);
  }

  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        border: Border(top: BorderSide(color: AppTheme.borderColor)),
      ),
      child: Column(
        children: [
          _buildSuggestedQuestions(),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _questionController,
                  decoration: InputDecoration(
                    hintText: 'Ask me anything about Physics, Chemistry, Math, or Biology...',
                    hintStyle: TextStyle(color: AppTheme.textTertiary),
                    filled: true,
                    fillColor: AppTheme.backgroundColor,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: BorderSide(color: AppTheme.borderColor),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: BorderSide(color: AppTheme.borderColor),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: BorderSide(color: AppTheme.primaryColor),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  maxLines: null,
                  textInputAction: TextInputAction.send,
                  onSubmitted: (_) => _sendMessage(),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  borderRadius: BorderRadius.circular(24),
                ),
                child: IconButton(
                  onPressed: _isLoading ? null : _sendMessage,
                  icon: _isLoading
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(Icons.send, color: Colors.white),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestedQuestions() {
    final suggestions = [
      'Explain Newton\'s laws',
      'Solve quadratic equations',
      'Organic chemistry basics',
      'Cell division process',
    ];

    return SizedBox(
      height: 32,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: suggestions.length,
        itemBuilder: (context, index) {
          final suggestion = suggestions[index];
          return GestureDetector(
            onTap: () => _selectSuggestion(suggestion),
            child: Container(
              margin: EdgeInsets.only(right: index < suggestions.length - 1 ? 8 : 0),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: AppTheme.primaryColor.withOpacity(0.3)),
              ),
              child: Text(
                suggestion,
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _selectTopic(String topic) {
    AppLogger.userAction('AI Tutor topic selected', {'topic': topic});
    
    _questionController.text = 'I want to learn about $topic';
    _sendMessage();
  }

  void _selectSuggestion(String suggestion) {
    AppLogger.userAction('AI Tutor suggestion selected', {'suggestion': suggestion});
    
    _questionController.text = suggestion;
    _sendMessage();
  }

  void _sendMessage() async {
    final question = _questionController.text.trim();
    if (question.isEmpty) return;

    AppLogger.userAction('AI Tutor question asked', {'question': question});

    // Add user message
    setState(() {
      _chatHistory.add({
        'type': 'user',
        'message': question,
        'timestamp': DateTime.now(),
      });
      _isLoading = true;
    });

    _questionController.clear();
    _scrollToBottom();

    // Simulate AI response
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _chatHistory.add({
        'type': 'ai',
        'message': _generateAIResponse(question),
        'timestamp': DateTime.now(),
      });
      _isLoading = false;
    });

    _scrollToBottom();
  }

  String _generateAIResponse(String question) {
    // Simple response generation based on keywords
    final lowerQuestion = question.toLowerCase();
    
    if (lowerQuestion.contains('newton') || lowerQuestion.contains('law')) {
      return 'Newton\'s laws of motion are three fundamental principles:\n\n1. First Law (Inertia): An object at rest stays at rest, and an object in motion stays in motion unless acted upon by an external force.\n\n2. Second Law: F = ma (Force equals mass times acceleration)\n\n3. Third Law: For every action, there is an equal and opposite reaction.\n\nWould you like me to explain any of these in more detail?';
    } else if (lowerQuestion.contains('quadratic')) {
      return 'To solve quadratic equations (ax² + bx + c = 0), you can use:\n\n1. Factoring method\n2. Completing the square\n3. Quadratic formula: x = (-b ± √(b²-4ac)) / 2a\n\nThe discriminant (b²-4ac) tells us:\n- If > 0: Two real solutions\n- If = 0: One real solution\n- If < 0: No real solutions\n\nWould you like me to work through a specific example?';
    } else if (lowerQuestion.contains('organic') || lowerQuestion.contains('chemistry')) {
      return 'Organic chemistry is the study of carbon-containing compounds. Key concepts include:\n\n1. Functional groups (alcohols, aldehydes, ketones, etc.)\n2. Isomerism (structural and stereoisomerism)\n3. Reaction mechanisms (substitution, elimination, addition)\n4. Nomenclature (IUPAC naming)\n\nCarbon can form 4 bonds and create chains, rings, and complex structures. What specific topic would you like to explore?';
    } else if (lowerQuestion.contains('cell') || lowerQuestion.contains('division')) {
      return 'Cell division occurs through two main processes:\n\n1. Mitosis: Produces two identical diploid cells for growth and repair\n   - Phases: Prophase, Metaphase, Anaphase, Telophase\n\n2. Meiosis: Produces four genetically different haploid gametes\n   - Two divisions: Meiosis I and Meiosis II\n   - Creates genetic diversity through crossing over\n\nBoth processes are crucial for life. Which aspect would you like me to explain further?';
    } else {
      return 'That\'s an interesting question! I\'d be happy to help you understand this topic better. Could you provide a bit more context or specify which subject area this relates to (Physics, Chemistry, Mathematics, or Biology)? This will help me give you a more detailed and accurate explanation.';
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else {
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    }
  }

  @override
  void dispose() {
    _questionController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}
